import React, { useEffect, useState } from 'react'
import { motion } from 'framer-motion'

const Confetti = () => {
  const [confettiPieces, setConfettiPieces] = useState([])

  useEffect(() => {
    const pieces = []
    const colors = ['#ff69b4', '#d63384', '#6f42c1', '#ffd700', '#ff6b6b', '#4ecdc4']
    const shapes = ['❤️', '🎉', '✨', '🎊', '💖', '🌟']
    
    for (let i = 0; i < 50; i++) {
      pieces.push({
        id: i,
        color: colors[Math.floor(Math.random() * colors.length)],
        shape: shapes[Math.floor(Math.random() * shapes.length)],
        x: Math.random() * window.innerWidth,
        y: -20,
        rotation: Math.random() * 360,
        size: Math.random() * 20 + 10,
        delay: Math.random() * 3,
        duration: Math.random() * 3 + 2
      })
    }
    setConfettiPieces(pieces)
  }, [])

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      width: '100%',
      height: '100%',
      pointerEvents: 'none',
      zIndex: 1000,
      overflow: 'hidden'
    }}>
      {confettiPieces.map((piece) => (
        <motion.div
          key={piece.id}
          initial={{
            x: piece.x,
            y: -20,
            rotate: 0,
            opacity: 0
          }}
          animate={{
            x: piece.x + (Math.random() - 0.5) * 200,
            y: window.innerHeight + 20,
            rotate: piece.rotation,
            opacity: [0, 1, 1, 0]
          }}
          transition={{
            duration: piece.duration,
            delay: piece.delay,
            ease: "easeOut"
          }}
          style={{
            position: 'absolute',
            fontSize: `${piece.size}px`,
            color: piece.color,
            userSelect: 'none'
          }}
        >
          {piece.shape}
        </motion.div>
      ))}
      
      {/* Balloon Animation */}
      {[...Array(5)].map((_, i) => (
        <motion.div
          key={`balloon-${i}`}
          initial={{
            x: Math.random() * window.innerWidth,
            y: window.innerHeight + 50,
            scale: 0
          }}
          animate={{
            x: Math.random() * window.innerWidth,
            y: -100,
            scale: 1
          }}
          transition={{
            duration: 4,
            delay: i * 0.5,
            ease: "easeOut"
          }}
          style={{
            position: 'absolute',
            fontSize: '3rem',
            userSelect: 'none'
          }}
        >
          🎈
        </motion.div>
      ))}
    </div>
  )
}

export default Confetti
