import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'

const FinalMessageSection = () => {
  const [stars, setStars] = useState([])
  const [showSmileButton, setShowSmileButton] = useState(false)
  const [showThankYou, setShowThankYou] = useState(false)

  useEffect(() => {
    // Generate random stars
    const starElements = []
    for (let i = 0; i < 100; i++) {
      starElements.push({
        id: i,
        x: Math.random() * 100,
        y: Math.random() * 100,
        size: Math.random() * 3 + 1,
        delay: Math.random() * 5,
        duration: Math.random() * 3 + 2
      })
    }
    setStars(starElements)

    // Show smile button after all text animations
    const timer = setTimeout(() => {
      setShowSmileButton(true)
    }, 8000)

    return () => clearTimeout(timer)
  }, [])

  const handleSmileClick = () => {
    setShowThankYou(true)
  }

  const messageLines = [
    "You are not just a part of my life.",
    "You are my calm, my chaos, my reason, and my dream.",
    "You've changed everything, <PERSON><PERSON><PERSON>.",
    "I just want to see you happy — always.",
    "",
    "Happy Birthday, Babu ❤️",
    "Always yours."
  ]

  return (
    <section className="final-message-section" id="final">
      {/* Animated Stars Background */}
      <div className="stars-container">
        {stars.map((star) => (
          <motion.div
            key={star.id}
            className="star"
            initial={{ opacity: 0, scale: 0 }}
            animate={{ 
              opacity: [0, 1, 0.5, 1], 
              scale: [0, 1, 0.8, 1],
              rotate: 360
            }}
            transition={{
              duration: star.duration,
              delay: star.delay,
              repeat: Infinity,
              repeatType: "reverse"
            }}
            style={{
              left: `${star.x}%`,
              top: `${star.y}%`,
              width: `${star.size}px`,
              height: `${star.size}px`
            }}
          />
        ))}
      </div>

      {/* Floating Hearts */}
      <div className="floating-elements">
        {[...Array(8)].map((_, i) => (
          <motion.div
            key={`heart-${i}`}
            className="floating-heart"
            initial={{ 
              x: Math.random() * window.innerWidth,
              y: window.innerHeight + 50,
              opacity: 0
            }}
            animate={{
              x: Math.random() * window.innerWidth,
              y: -50,
              opacity: [0, 0.7, 0]
            }}
            transition={{
              duration: 8,
              delay: i * 2,
              repeat: Infinity,
              ease: "linear"
            }}
          >
            💖
          </motion.div>
        ))}
      </div>

      <div className="message-container">
        <div className="message-content">
          {messageLines.map((line, index) => (
            <motion.div
              key={index}
              className={`message-line ${line === "" ? "spacer" : ""}`}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ 
                duration: 1, 
                delay: index * 0.8,
                ease: "easeOut"
              }}
              viewport={{ once: true }}
            >
              {line}
            </motion.div>
          ))}

          {/* Glowing Heart */}
          <motion.div
            className="glowing-heart"
            initial={{ opacity: 0, scale: 0 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 1, delay: 6 }}
            viewport={{ once: true }}
          >
            <motion.div
              animate={{
                scale: [1, 1.2, 1],
                rotate: [0, 5, -5, 0]
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                repeatType: "reverse"
              }}
            >
              💖
            </motion.div>
          </motion.div>

          {/* Smile Button */}
          {showSmileButton && !showThankYou && (
            <motion.button
              className="smile-button"
              initial={{ opacity: 0, scale: 0 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8 }}
              onClick={handleSmileClick}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Tap here if you smiled 🥹
            </motion.button>
          )}

          {/* Thank You Message */}
          {showThankYou && (
            <motion.div
              className="thank-you-message"
              initial={{ opacity: 0, scale: 0.5 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 1 }}
            >
              <div className="thank-you-text">
                Thank you for being you, Roshni ✨<br />
                You make every day brighter! 🌟
              </div>
              <div className="celebration-emojis">
                🎉 🎂 💖 🌸 ✨ 🎈 💕 🌟
              </div>
            </motion.div>
          )}
        </div>
      </div>

      <style jsx>{`
        .final-message-section {
          min-height: 100vh;
          background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 4rem 2rem;
          overflow: hidden;
        }

        .stars-container {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          pointer-events: none;
        }

        .star {
          position: absolute;
          background: #ffffff;
          border-radius: 50%;
          box-shadow: 0 0 10px #ffffff;
        }

        .floating-elements {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          pointer-events: none;
        }

        .floating-heart {
          position: absolute;
          font-size: 2rem;
          user-select: none;
        }

        .message-container {
          max-width: 800px;
          width: 100%;
          text-align: center;
          z-index: 10;
          position: relative;
        }

        .message-content {
          color: #ffffff;
        }

        .message-line {
          font-family: 'Poppins', sans-serif;
          font-size: clamp(1.2rem, 4vw, 2rem);
          line-height: 1.6;
          margin-bottom: 1.5rem;
          font-weight: 400;
          text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .message-line.spacer {
          height: 2rem;
          margin-bottom: 2rem;
        }

        .glowing-heart {
          font-size: 4rem;
          margin: 3rem 0;
          filter: drop-shadow(0 0 20px #ff69b4);
        }

        .smile-button {
          background: linear-gradient(135deg, #ff69b4, #d63384);
          color: white;
          border: none;
          padding: 1rem 2rem;
          font-size: 1.2rem;
          border-radius: 50px;
          cursor: pointer;
          font-family: 'Poppins', sans-serif;
          font-weight: 500;
          box-shadow: 0 10px 30px rgba(255, 105, 180, 0.4);
          margin-top: 3rem;
          transition: all 0.3s ease;
        }

        .smile-button:hover {
          box-shadow: 0 15px 40px rgba(255, 105, 180, 0.6);
          transform: translateY(-2px);
        }

        .thank-you-message {
          margin-top: 3rem;
        }

        .thank-you-text {
          font-family: 'Dancing Script', cursive;
          font-size: clamp(1.5rem, 5vw, 2.5rem);
          color: #ff69b4;
          margin-bottom: 2rem;
          font-weight: 600;
          text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .celebration-emojis {
          font-size: 2rem;
          letter-spacing: 1rem;
          animation: bounce 2s ease-in-out infinite;
        }

        @keyframes bounce {
          0%, 20%, 50%, 80%, 100% {
            transform: translateY(0);
          }
          40% {
            transform: translateY(-10px);
          }
          60% {
            transform: translateY(-5px);
          }
        }

        @media (max-width: 768px) {
          .final-message-section {
            padding: 2rem 1rem;
          }
          
          .message-line {
            margin-bottom: 1rem;
          }
          
          .glowing-heart {
            font-size: 3rem;
            margin: 2rem 0;
          }
          
          .smile-button {
            padding: 0.8rem 1.5rem;
            font-size: 1rem;
          }
          
          .celebration-emojis {
            font-size: 1.5rem;
            letter-spacing: 0.5rem;
          }
        }
      `}</style>
    </section>
  )
}

export default FinalMessageSection
