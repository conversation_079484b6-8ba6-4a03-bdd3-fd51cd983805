.app {
  min-height: 100vh;
  width: 100%;
  overflow-x: hidden;
  background: linear-gradient(135deg, #ffeef8 0%, #f8e8ff 50%, #ffe8f0 100%);
  font-family: 'Poppins', sans-serif;
}

.app-container {
  width: 100%;
  min-height: 100vh;
}

/* Hero Section Styles */
.hero-section {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 2rem;
  background: linear-gradient(135deg, #ffeef8 0%, #f8e8ff 50%, #ffe8f0 100%);
  overflow: hidden;
}

.hero-content {
  text-align: center;
  z-index: 10;
  position: relative;
}

.hero-title {
  font-family: 'Dancing Script', cursive;
  font-size: clamp(2.5rem, 8vw, 6rem);
  font-weight: 700;
  color: #d63384;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(214, 51, 132, 0.3);
}

.hero-subtitle {
  font-size: clamp(1rem, 3vw, 1.5rem);
  color: #6f42c1;
  margin-bottom: 2rem;
  font-weight: 500;
}

.cake-container {
  width: 300px;
  height: 300px;
  margin: 2rem auto;
  position: relative;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.cake-container:hover {
  transform: scale(1.05);
}

.celebration-message {
  font-family: 'Dancing Script', cursive;
  font-size: clamp(1.5rem, 4vw, 2.5rem);
  color: #d63384;
  margin-top: 2rem;
  font-weight: 600;
  text-shadow: 1px 1px 2px rgba(214, 51, 132, 0.3);
}

.scroll-indicator {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  color: #6f42c1;
  font-size: 2rem;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateX(-50%) translateY(0);
  }
  40% {
    transform: translateX(-50%) translateY(-10px);
  }
  60% {
    transform: translateX(-50%) translateY(-5px);
  }
}

/* Floating Hearts Background */
.floating-hearts {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
}

.heart {
  position: absolute;
  color: #ff69b4;
  font-size: 1.5rem;
  opacity: 0.7;
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 0.7;
  }
  90% {
    opacity: 0.7;
  }
  100% {
    transform: translateY(-100px) rotate(360deg);
    opacity: 0;
  }
}

/* Additional Mobile Optimizations */
.audio-toggle {
  position: fixed !important;
  top: 20px !important;
  right: 20px !important;
  z-index: 1000 !important;
  background: rgba(214, 51, 132, 0.9) !important;
  border: none !important;
  border-radius: 50% !important;
  width: 50px !important;
  height: 50px !important;
  font-size: 1.2rem !important;
  cursor: pointer !important;
  box-shadow: 0 4px 15px rgba(214, 51, 132, 0.4) !important;
  transition: all 0.3s ease !important;
}

.audio-toggle:hover {
  transform: scale(1.1) !important;
  box-shadow: 0 6px 20px rgba(214, 51, 132, 0.6) !important;
}

/* Smooth transitions for all interactive elements */
* {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

/* Improved touch targets for mobile */
button, .cake-container, .photo-item, .envelope {
  min-height: 44px;
  min-width: 44px;
}

/* Better text readability */
.hero-title, .section-title {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-section {
    padding: 1rem;
    min-height: 100vh;
  }

  .cake-container {
    width: 250px;
    height: 250px;
    margin: 1.5rem auto;
  }

  .hero-title {
    margin-bottom: 0.5rem;
  }

  .hero-subtitle {
    margin-bottom: 1rem;
  }

  .celebration-message {
    margin-top: 1.5rem;
    padding: 0 1rem;
  }

  .audio-toggle {
    width: 45px !important;
    height: 45px !important;
    font-size: 1.1rem !important;
  }
}

@media (max-width: 480px) {
  .hero-section {
    padding: 0.5rem;
  }

  .cake-container {
    width: 200px;
    height: 200px;
    margin: 1rem auto;
  }

  .hero-title {
    font-size: clamp(2rem, 10vw, 4rem);
  }

  .celebration-message {
    font-size: clamp(1.2rem, 5vw, 2rem);
  }
}

/* Landscape mobile optimization */
@media (max-height: 600px) and (orientation: landscape) {
  .hero-section {
    padding: 1rem;
  }

  .cake-container {
    width: 180px;
    height: 180px;
    margin: 1rem auto;
  }

  .hero-title {
    font-size: clamp(1.8rem, 6vw, 3rem);
    margin-bottom: 0.3rem;
  }

  .hero-subtitle {
    font-size: clamp(0.9rem, 2.5vw, 1.2rem);
    margin-bottom: 0.5rem;
  }
}
