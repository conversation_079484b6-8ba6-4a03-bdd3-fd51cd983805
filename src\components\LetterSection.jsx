import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'

const LetterSection = () => {
  const [isEnvelopeOpen, setIsEnvelopeOpen] = useState(false)
  const [showLetter, setShowLetter] = useState(false)
  const [typedText, setTypedText] = useState('')
  const [isTyping, setIsTyping] = useState(false)

  const letterText = `My Dearest Roshni,

Some feelings are too deep for words,
But today, I just want to say —

You've made my world brighter, my days softer, and my heart fuller.
Every moment with you feels like a beautiful dream that I never want to wake up from.

You are not just special to me, you are everything.
Your smile lights up my darkest days,
Your laugh is my favorite melody,
And your presence makes everything feel right.

Thank you for being the incredible person you are.
Thank you for bringing so much joy and love into my life.

Happy Birthday, my beautiful Roshni 💗

With all my love,
Always yours ❤️`

  const handleEnvelopeClick = () => {
    if (!isEnvelopeOpen) {
      setIsEnvelopeOpen(true)
      setTimeout(() => {
        setShowLetter(true)
        setIsTyping(true)
      }, 1000)
    }
  }

  useEffect(() => {
    if (isTyping && typedText.length < letterText.length) {
      const timeout = setTimeout(() => {
        setTypedText(letterText.slice(0, typedText.length + 1))
      }, 50)
      return () => clearTimeout(timeout)
    } else if (typedText.length === letterText.length) {
      setIsTyping(false)
    }
  }, [typedText, isTyping, letterText])

  return (
    <section className="letter-section" id="letter">
      <div className="letter-container">
        <motion.h2
          className="section-title"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          A Special Message
        </motion.h2>

        <motion.div
          className="envelope-container"
          initial={{ opacity: 0, scale: 0.8 }}
          whileInView={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          viewport={{ once: true }}
        >
          <div 
            className={`envelope ${isEnvelopeOpen ? 'open' : ''}`}
            onClick={handleEnvelopeClick}
          >
            <div className="envelope-back"></div>
            <div className="envelope-front"></div>
            <div className="envelope-flap"></div>
            
            {!isEnvelopeOpen && (
              <motion.p
                className="envelope-instruction"
                animate={{ opacity: [0.7, 1, 0.7] }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                Tap to open the letter...
              </motion.p>
            )}
          </div>
        </motion.div>

        <AnimatePresence>
          {showLetter && (
            <motion.div
              className="letter-content"
              initial={{ opacity: 0, y: 50, scale: 0.9 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              transition={{ duration: 0.8 }}
            >
              <div className="letter-paper">
                <div className="letter-text">
                  {typedText}
                  {isTyping && <span className="cursor">|</span>}
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      <style jsx>{`
        .letter-section {
          min-height: 100vh;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 4rem 2rem;
          background: linear-gradient(135deg, #f8e8ff 0%, #ffeef8 100%);
        }

        .letter-container {
          max-width: 800px;
          width: 100%;
          text-align: center;
        }

        .section-title {
          font-family: 'Dancing Script', cursive;
          font-size: clamp(2.5rem, 6vw, 4rem);
          color: #d63384;
          margin-bottom: 3rem;
          font-weight: 600;
        }

        .envelope-container {
          margin: 3rem auto;
          perspective: 1000px;
        }

        .envelope {
          position: relative;
          width: 300px;
          height: 200px;
          margin: 0 auto;
          cursor: pointer;
          transform-style: preserve-3d;
          transition: all 0.8s ease;
        }

        .envelope.open {
          transform: rotateY(15deg) rotateX(10deg);
        }

        .envelope-back {
          position: absolute;
          width: 100%;
          height: 100%;
          background: linear-gradient(135deg, #d63384, #6f42c1);
          border-radius: 8px;
          box-shadow: 0 10px 30px rgba(214, 51, 132, 0.3);
        }

        .envelope-front {
          position: absolute;
          width: 100%;
          height: 100%;
          background: linear-gradient(135deg, #ff69b4, #d63384);
          border-radius: 8px;
          transform: translateZ(2px);
          transition: all 0.8s ease;
        }

        .envelope.open .envelope-front {
          transform: translateZ(2px) translateY(-20px);
        }

        .envelope-flap {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 50%;
          background: linear-gradient(135deg, #6f42c1, #d63384);
          border-radius: 8px 8px 0 0;
          transform-origin: bottom;
          transition: all 0.8s ease;
          transform: translateZ(4px);
        }

        .envelope.open .envelope-flap {
          transform: translateZ(4px) rotateX(-180deg);
        }

        .envelope-instruction {
          position: absolute;
          bottom: -40px;
          left: 50%;
          transform: translateX(-50%);
          color: #6f42c1;
          font-weight: 500;
          font-size: 1.1rem;
        }

        .letter-content {
          margin-top: 3rem;
        }

        .letter-paper {
          background: #ffffff;
          border-radius: 12px;
          padding: 3rem;
          box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
          max-width: 600px;
          margin: 0 auto;
          position: relative;
        }

        .letter-paper::before {
          content: '';
          position: absolute;
          top: 0;
          left: 60px;
          width: 2px;
          height: 100%;
          background: #ff69b4;
          opacity: 0.3;
        }

        .letter-text {
          font-family: 'Poppins', sans-serif;
          font-size: 1.1rem;
          line-height: 1.8;
          color: #333;
          text-align: left;
          white-space: pre-line;
        }

        .cursor {
          animation: blink 1s infinite;
          color: #d63384;
          font-weight: bold;
        }

        @keyframes blink {
          0%, 50% { opacity: 1; }
          51%, 100% { opacity: 0; }
        }

        @media (max-width: 768px) {
          .letter-section {
            padding: 2rem 1rem;
          }
          
          .envelope {
            width: 250px;
            height: 160px;
          }
          
          .letter-paper {
            padding: 2rem 1.5rem;
          }
          
          .letter-text {
            font-size: 1rem;
          }
        }
      `}</style>
    </section>
  )
}

export default LetterSection
