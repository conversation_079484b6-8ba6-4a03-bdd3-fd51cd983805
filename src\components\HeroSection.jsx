import React, { useState, useRef, useEffect } from 'react'
import { Canvas } from '@react-three/fiber'
import { OrbitControls, Text3D, Center } from '@react-three/drei'
import { motion, AnimatePresence } from 'framer-motion'
import * as THREE from 'three'
import Confetti from './Confetti'
import FloatingHearts from './FloatingHearts'

// 3D Cake Component
function Cake({ onClick, isClicked }) {
  const cakeRef = useRef()
  const candleRef = useRef()

  useEffect(() => {
    if (cakeRef.current) {
      cakeRef.current.rotation.y += 0.005
    }
  })

  return (
    <group ref={cakeRef} onClick={onClick}>
      {/* Cake Base */}
      <mesh position={[0, -1, 0]}>
        <cylinderGeometry args={[2, 2, 1.5, 32]} />
        <meshStandardMaterial color="#8B4513" />
      </mesh>
      
      {/* Cake Layer 1 */}
      <mesh position={[0, -0.2, 0]}>
        <cylinderGeometry args={[1.8, 1.8, 1, 32]} />
        <meshStandardMaterial color="#FFB6C1" />
      </mesh>
      
      {/* Cake Layer 2 */}
      <mesh position={[0, 0.5, 0]}>
        <cylinderGeometry args={[1.5, 1.5, 0.8, 32]} />
        <meshStandardMaterial color="#FFC0CB" />
      </mesh>
      
      {/* Icing */}
      <mesh position={[0, 1, 0]}>
        <cylinderGeometry args={[1.5, 1.5, 0.1, 32]} />
        <meshStandardMaterial color="#FFFFFF" />
      </mesh>
      
      {/* Candle */}
      <group ref={candleRef} position={[0, 1.5, 0]}>
        <mesh>
          <cylinderGeometry args={[0.05, 0.05, 0.8, 8]} />
          <meshStandardMaterial color="#FFFF00" />
        </mesh>
        
        {/* Flame */}
        {!isClicked && (
          <mesh position={[0, 0.5, 0]}>
            <sphereGeometry args={[0.1, 8, 8]} />
            <meshStandardMaterial 
              color="#FF4500" 
              emissive="#FF4500"
              emissiveIntensity={0.5}
            />
          </mesh>
        )}
      </group>
      
      {/* Name on Cake */}
      <Center position={[0, 1.1, 0]} rotation={[-Math.PI / 2, 0, 0]}>
        <Text3D
          font="/fonts/dancing_script.json"
          size={0.3}
          height={0.02}
          curveSegments={12}
        >
          Roshni
          <meshStandardMaterial color="#d63384" />
        </Text3D>
      </Center>
    </group>
  )
}

function HeroSection() {
  const [candleClicked, setCandleClicked] = useState(false)
  const [showCelebration, setShowCelebration] = useState(false)
  const [audioPlaying, setAudioPlaying] = useState(false)
  const audioRef = useRef(null)

  const handleCandleClick = () => {
    if (!candleClicked) {
      setCandleClicked(true)
      setShowCelebration(true)
      
      // Play birthday song
      if (audioRef.current) {
        audioRef.current.play().catch(console.error)
        setAudioPlaying(true)
      }
      
      // Show scroll indicator after celebration
      setTimeout(() => {
        const scrollIndicator = document.querySelector('.scroll-indicator')
        if (scrollIndicator) {
          scrollIndicator.style.display = 'block'
        }
      }, 3000)
    }
  }

  const toggleAudio = () => {
    if (audioRef.current) {
      if (audioPlaying) {
        audioRef.current.pause()
        setAudioPlaying(false)
      } else {
        audioRef.current.play().catch(console.error)
        setAudioPlaying(true)
      }
    }
  }

  return (
    <section className="hero-section" id="hero">
      <FloatingHearts />
      
      <div className="hero-content">
        <motion.h1 
          className="hero-title"
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, delay: 0.5 }}
        >
          Happy Birthday
        </motion.h1>
        
        <motion.p 
          className="hero-subtitle"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, delay: 1 }}
        >
          To someone very special ✨
        </motion.p>
        
        <motion.div 
          className="cake-container"
          initial={{ opacity: 0, scale: 0.5 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 1, delay: 1.5 }}
        >
          <Canvas camera={{ position: [0, 0, 8], fov: 50 }}>
            <ambientLight intensity={0.6} />
            <pointLight position={[10, 10, 10]} intensity={1} />
            <pointLight position={[-10, -10, -10]} intensity={0.5} />
            <Cake onClick={handleCandleClick} isClicked={candleClicked} />
            <OrbitControls enableZoom={false} enablePan={false} />
          </Canvas>
        </motion.div>
        
        <AnimatePresence>
          {showCelebration && (
            <motion.div
              className="celebration-message"
              initial={{ opacity: 0, scale: 0.5 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8 }}
            >
              Happy Birthday Babu 🎉<br />
              You are the light of my life, Roshni ❤️
            </motion.div>
          )}
        </AnimatePresence>
        
        {!candleClicked && (
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 1, delay: 2 }}
            style={{ 
              marginTop: '1rem', 
              color: '#6f42c1', 
              fontSize: '1.1rem',
              fontWeight: '500'
            }}
          >
            Click the candle to make a wish! 🕯️
          </motion.p>
        )}
      </div>
      
      {/* Confetti Animation */}
      <AnimatePresence>
        {showCelebration && <Confetti />}
      </AnimatePresence>
      
      {/* Audio Controls */}
      {showCelebration && (
        <motion.button
          className="audio-toggle"
          onClick={toggleAudio}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1 }}
          style={{
            position: 'fixed',
            top: '20px',
            right: '20px',
            zIndex: 1000,
            background: 'rgba(214, 51, 132, 0.9)',
            border: 'none',
            borderRadius: '50%',
            width: '50px',
            height: '50px',
            fontSize: '1.2rem',
            cursor: 'pointer'
          }}
        >
          {audioPlaying ? '🔊' : '🔇'}
        </motion.button>
      )}
      
      {/* Scroll Indicator */}
      <motion.div 
        className="scroll-indicator"
        initial={{ opacity: 0 }}
        animate={{ opacity: showCelebration ? 1 : 0 }}
        transition={{ delay: 3 }}
        style={{ display: showCelebration ? 'block' : 'none' }}
      >
        ↓
      </motion.div>
      
      {/* Hidden Audio Element */}
      <audio
        ref={audioRef}
        loop
        preload="auto"
      >
        <source src="/audio/happy-birthday.mp3" type="audio/mpeg" />
        <source src="/audio/happy-birthday.ogg" type="audio/ogg" />
      </audio>
    </section>
  )
}

export default HeroSection
