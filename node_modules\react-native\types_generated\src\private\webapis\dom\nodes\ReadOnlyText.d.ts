/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @generated SignedSource<<24330efa5e7783fe376a56894db8e858>>
 *
 * This file was translated from Flow by scripts/build-types/index.js.
 * Original file: packages/react-native/src/private/webapis/dom/nodes/ReadOnlyText.js
 */

import ReadOnlyCharacterData from "./ReadOnlyCharacterData";
declare class ReadOnlyText extends ReadOnlyCharacterData {
  get nodeName(): string;
  get nodeType(): number;
}
export default ReadOnlyText;
