import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'

const MemoriesSection = () => {
  const [selectedPhoto, setSelectedPhoto] = useState(null)

  // Placeholder photos with captions - you'll replace these with actual photos
  const memories = [
    {
      id: 1,
      src: '/images/memory1.jpg',
      alt: 'Beautiful Memory 1',
      caption: 'The day I laughed without a reason.',
      placeholder: '🌸'
    },
    {
      id: 2,
      src: '/images/memory2.jpg',
      alt: 'Beautiful Memory 2',
      caption: 'That one moment I\'ll never forget.',
      placeholder: '✨'
    },
    {
      id: 3,
      src: '/images/memory3.jpg',
      alt: 'Beautiful Memory 3',
      caption: 'Your smile in this photo still melts me.',
      placeholder: '💫'
    },
    {
      id: 4,
      src: '/images/memory4.jpg',
      alt: 'Beautiful Memory 4',
      caption: 'When time stood still for us.',
      placeholder: '🌟'
    },
    {
      id: 5,
      src: '/images/memory5.jpg',
      alt: 'Beautiful Memory 5',
      caption: 'Pure happiness captured forever.',
      placeholder: '💖'
    },
    {
      id: 6,
      src: '/images/memory6.jpg',
      alt: 'Beautiful Memory 6',
      caption: 'The moment I knew you were special.',
      placeholder: '🌺'
    }
  ]

  const openLightbox = (photo) => {
    setSelectedPhoto(photo)
  }

  const closeLightbox = () => {
    setSelectedPhoto(null)
  }

  return (
    <section className="memories-section" id="memories">
      <div className="memories-container">
        <motion.h2
          className="section-title"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          Our Beautiful Memories
        </motion.h2>

        <motion.p
          className="section-subtitle"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true }}
        >
          Every moment with you is a treasure ✨
        </motion.p>

        <div className="photo-grid">
          {memories.map((memory, index) => (
            <motion.div
              key={memory.id}
              className="photo-item"
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ scale: 1.05, y: -5 }}
              onClick={() => openLightbox(memory)}
            >
              <div className="photo-placeholder">
                <div className="placeholder-icon">{memory.placeholder}</div>
                <p className="placeholder-text">Photo {memory.id}</p>
                <p className="photo-caption-preview">{memory.caption}</p>
              </div>
            </motion.div>
          ))}
        </div>

        <motion.p
          className="upload-note"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.8 }}
          viewport={{ once: true }}
        >
          💝 Add your favorite photos to make this even more special
        </motion.p>
      </div>

      {/* Lightbox Modal */}
      <AnimatePresence>
        {selectedPhoto && (
          <motion.div
            className="lightbox-overlay"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={closeLightbox}
          >
            <motion.div
              className="lightbox-content"
              initial={{ scale: 0.5, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.5, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
            >
              <button className="close-button" onClick={closeLightbox}>
                ×
              </button>
              <div className="lightbox-photo">
                <div className="lightbox-placeholder">
                  <div className="placeholder-icon large">{selectedPhoto.placeholder}</div>
                  <p className="placeholder-text large">Photo {selectedPhoto.id}</p>
                </div>
              </div>
              <div className="lightbox-caption">
                <p>{selectedPhoto.caption}</p>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      <style jsx>{`
        .memories-section {
          min-height: 100vh;
          padding: 4rem 2rem;
          background: linear-gradient(135deg, #ffeef8 0%, #f0e8ff 100%);
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .memories-container {
          max-width: 1200px;
          width: 100%;
          text-align: center;
        }

        .section-title {
          font-family: 'Dancing Script', cursive;
          font-size: clamp(2.5rem, 6vw, 4rem);
          color: #d63384;
          margin-bottom: 1rem;
          font-weight: 600;
        }

        .section-subtitle {
          font-size: clamp(1rem, 3vw, 1.3rem);
          color: #6f42c1;
          margin-bottom: 3rem;
          font-weight: 500;
        }

        .photo-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 2rem;
          margin-bottom: 3rem;
        }

        .photo-item {
          cursor: pointer;
          border-radius: 16px;
          overflow: hidden;
          box-shadow: 0 10px 30px rgba(214, 51, 132, 0.2);
          transition: all 0.3s ease;
          background: white;
        }

        .photo-placeholder {
          aspect-ratio: 4/3;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          background: linear-gradient(135deg, #ffeef8, #f8e8ff);
          padding: 2rem;
          text-align: center;
        }

        .placeholder-icon {
          font-size: 3rem;
          margin-bottom: 1rem;
        }

        .placeholder-icon.large {
          font-size: 5rem;
        }

        .placeholder-text {
          font-size: 1.2rem;
          color: #6f42c1;
          font-weight: 600;
          margin-bottom: 1rem;
        }

        .placeholder-text.large {
          font-size: 1.5rem;
          margin-bottom: 2rem;
        }

        .photo-caption-preview {
          font-size: 0.9rem;
          color: #666;
          font-style: italic;
          line-height: 1.4;
        }

        .upload-note {
          color: #6f42c1;
          font-size: 1.1rem;
          font-weight: 500;
          font-style: italic;
        }

        .lightbox-overlay {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(0, 0, 0, 0.9);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 2000;
          padding: 2rem;
        }

        .lightbox-content {
          position: relative;
          max-width: 90vw;
          max-height: 90vh;
          background: white;
          border-radius: 16px;
          overflow: hidden;
          box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }

        .close-button {
          position: absolute;
          top: 1rem;
          right: 1rem;
          background: rgba(214, 51, 132, 0.9);
          color: white;
          border: none;
          border-radius: 50%;
          width: 40px;
          height: 40px;
          font-size: 1.5rem;
          cursor: pointer;
          z-index: 10;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .lightbox-photo {
          width: 100%;
          min-height: 400px;
        }

        .lightbox-placeholder {
          width: 100%;
          height: 400px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          background: linear-gradient(135deg, #ffeef8, #f8e8ff);
        }

        .lightbox-caption {
          padding: 2rem;
          text-align: center;
          background: white;
        }

        .lightbox-caption p {
          font-size: 1.2rem;
          color: #333;
          font-style: italic;
          line-height: 1.6;
        }

        @media (max-width: 768px) {
          .memories-section {
            padding: 2rem 1rem;
          }
          
          .photo-grid {
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
          }
          
          .photo-placeholder {
            padding: 1.5rem;
          }
          
          .lightbox-content {
            margin: 1rem;
          }
          
          .lightbox-placeholder {
            height: 300px;
          }
          
          .lightbox-caption {
            padding: 1.5rem;
          }
        }
      `}</style>
    </section>
  )
}

export default MemoriesSection
