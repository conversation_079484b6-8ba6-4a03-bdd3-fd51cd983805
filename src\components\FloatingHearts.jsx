import React, { useEffect, useState } from 'react'
import { motion } from 'framer-motion'

const FloatingHearts = () => {
  const [hearts, setHearts] = useState([])

  useEffect(() => {
    const heartElements = []
    const heartShapes = ['💖', '💕', '💗', '💝', '❤️', '💜']
    
    for (let i = 0; i < 15; i++) {
      heartElements.push({
        id: i,
        shape: heartShapes[Math.floor(Math.random() * heartShapes.length)],
        x: Math.random() * 100,
        delay: Math.random() * 10,
        duration: Math.random() * 10 + 15,
        size: Math.random() * 1.5 + 0.8
      })
    }
    setHearts(heartElements)
  }, [])

  return (
    <div className="floating-hearts">
      {hearts.map((heart) => (
        <motion.div
          key={heart.id}
          className="heart"
          initial={{
            x: `${heart.x}vw`,
            y: '100vh',
            opacity: 0,
            scale: heart.size
          }}
          animate={{
            x: `${heart.x + (Math.random() - 0.5) * 20}vw`,
            y: '-10vh',
            opacity: [0, 0.7, 0.7, 0],
            rotate: 360
          }}
          transition={{
            duration: heart.duration,
            delay: heart.delay,
            repeat: Infinity,
            ease: "linear"
          }}
          style={{
            position: 'absolute',
            fontSize: '1.5rem',
            userSelect: 'none',
            pointerEvents: 'none'
          }}
        >
          {heart.shape}
        </motion.div>
      ))}
    </div>
  )
}

export default FloatingHearts
