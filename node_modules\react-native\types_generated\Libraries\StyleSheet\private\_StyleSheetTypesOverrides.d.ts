/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @generated SignedSource<<d90f671ce2cec7ccf24d9e3a1dc7b5ed>>
 *
 * This file was translated from Flow by scripts/build-types/index.js.
 * Original file: packages/react-native/Libraries/StyleSheet/private/_StyleSheetTypesOverrides.js
 */

export type ____DangerouslyImpreciseStyle_InternalOverrides = Readonly<{}>;
export type ____ImageStyle_InternalOverrides = Readonly<{}>;
export type ____ShadowStyle_InternalOverrides = Readonly<{}>;
export type ____TextStyle_InternalOverrides = Readonly<{}>;
export type ____ViewStyle_InternalOverrides = Readonly<{}>;
