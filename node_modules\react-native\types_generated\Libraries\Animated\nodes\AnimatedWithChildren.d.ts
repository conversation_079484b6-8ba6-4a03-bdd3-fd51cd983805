/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @generated SignedSource<<847d6fd45c338c692154216c07d4a5c3>>
 *
 * This file was translated from Flow by scripts/build-types/index.js.
 * Original file: packages/react-native/Libraries/Animated/nodes/AnimatedWithChildren.js
 */

import AnimatedNode from "./AnimatedNode";
declare class AnimatedWithChildren extends AnimatedNode {}
export default AnimatedWithChildren;
