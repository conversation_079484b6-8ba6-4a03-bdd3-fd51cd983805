# 🎂 Happy Birthday Roshni - Interactive Birthday Website

A beautiful, interactive, and mobile-optimized birthday website created with love for someone very special.

## ✨ Features

- **Interactive 3D Cake**: Click the candle to blow it out and start the celebration
- **Animated Confetti & Balloons**: Beautiful celebration animations
- **Heartfelt Letter**: Animated envelope that opens to reveal a personal message
- **Memories Section**: Photo grid with lightbox functionality (ready for your photos)
- **Final Message**: Starry night background with floating hearts and interactive elements
- **Mobile Optimized**: Fully responsive design that works perfectly on all devices
- **Professional Design**: Clean, elegant, and emotionally engaging

## 🚀 Quick Start

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Start development server**:
   ```bash
   npm run dev
   ```

3. **Build for production**:
   ```bash
   npm run build
   ```

## 📸 Adding Your Photos

1. Add your photos to the `public/images/` folder
2. Name them `memory1.jpg`, `memory2.jpg`, etc. (up to `memory6.jpg`)
3. The website will automatically display them in the memories section

## 🎵 Adding Birthday Music

1. Add your birthday song file to `public/audio/`
2. Name it `happy-birthday.mp3` (or update the path in `HeroSection.jsx`)
3. The music will play automatically when the candle is clicked

## 🌐 Deploy to Vercel

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Deploy with one click!

Or use Vercel CLI:
```bash
npm install -g vercel
vercel
```

## 🎨 Customization

- **Colors**: Edit CSS variables in `src/index.css`
- **Text**: Update messages in the component files
- **Animations**: Modify Framer Motion animations in each component
- **3D Elements**: Customize the cake in `src/components/HeroSection.jsx`

## 📱 Mobile Features

- Touch-friendly interactions
- Optimized animations for mobile performance
- Responsive design for all screen sizes
- Landscape mode support

## 💝 Made with Love

This website was created to celebrate someone truly special. Every animation, every word, and every detail was crafted with care and love.

---

*"You are not just a part of my life. You are my calm, my chaos, my reason, and my dream."*
